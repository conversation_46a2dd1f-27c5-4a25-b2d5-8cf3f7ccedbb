<template>
  <div class="detail-content">
    <a-descriptions
      :data="baseInfoData"
      layout="vertical"
      :column="2"
      table-layout="fixed"
    >
      <template #label="{ label }"> {{ label }}： </template>
      <template #value="{ value }">
        {{ value ?? '-' }}
      </template>
    </a-descriptions>
    <TaskDetailTabs :record="record" :current-page="currentPage" />
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { platformAccounts } from '@/views/task/account-task/constants';
  import TaskDetailTabs from './TaskDetailTabs.vue';

  const props = defineProps<{ record: any; currentPage: any }>();
  defineOptions({
    inheritAttrs: false,
  });

  const baseInfoData = computed(() => {
    const media = platformAccounts.find(
      (item) => item.mediaType === props.currentPage.mediaType
    )?.label;
    return [
      {
        label: '任务名称',
        value: props.record.taskName,
      },
      {
        label: '媒体',
        value: media,
      },
      {
        label: '操作类型',
        value: props.record.taskTypeName,
      },
      {
        label: '操作结果',
        value: props.record.statusName,
      },
    ];
  });
</script>

<style scoped lang="less">
  @import '@/styles/table.less';

  .detail-content {
    flex: 1;

    > .arco-descriptions {
      padding-left: 16px;
    }
  }
</style>
