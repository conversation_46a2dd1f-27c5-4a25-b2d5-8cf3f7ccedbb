<template>
  <a-tabs default-active-key="1">
    <a-tab-pane
      v-for="(tab, index) in tabsConfig"
      :key="tab.key"
      :title="tab.title"
    >
      <a-table
        :columns="columnsList[index]"
        :data="tableDataList[index]"
        :bordered="{ headerCell: true }"
        column-resizable
        :scroll="{ x: '100%', y: '100%' }"
      >
        <template #operationContent="{ record: row }">
          <a-tooltip position="tl">
            <template #content>
              <div style="max-height: 300px; overflow-y: auto">
                <span v-for="(item, index) in row.showList" :key="index">
                  <span style="margin-right: 5px; word-break: break-all">
                    {{ item.oldPropertyName }}：{{ item.oldTargetName }}
                  </span>
                  <span> >> </span>
                  <span style="word-break: break-all">
                    {{ item.newTargetName }}
                  </span>
                  <br />
                </span>
              </div>
            </template>
            <span class="operation-content">
              <span v-for="(item, index) in row.showList" :key="index">
                <span style="margin-right: 5px">
                  {{ item.oldPropertyName }}：{{ item.oldTargetName }}
                </span>
                <span> >> </span>
                <span>
                  {{ item.newTargetName }}
                </span>
                <br />
              </span>
            </span>
          </a-tooltip>
        </template>
        <template #operations="{ record: row }">
          <a-button type="text" @click="handleCancel(row)">取消</a-button>
        </template>
      </a-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { operationTask } from '@/api/task/operation-task';
  import {
    mediaPlatformMap,
    MediaType,
  } from '@/views/promotion/hooks/authorization';
  import { Message, Modal } from '@arco-design/web-vue';
  import {
    taskTableStatus,
    TaskStatusMap,
  } from '@/views/task/account-task/constants';

  const props = defineProps<{ record: any; currentPage: any }>();
  defineOptions({
    inheritAttrs: false,
  });

  const baseInfo = ref<any>([]);
  const tableDataList = ref<any>([]);
  const operationTargetList = ref<any>([]);

  // Tab 配置：根据平台动态生成
  const tabsConfig = computed(() => {
    if (props.currentPage.mediaType === MediaType.TOU_TIAO) {
      return [
        { key: '1', title: '账户信息' },
        { key: '2', title: '项目信息' },
        { key: '3', title: '广告信息' },
        { key: '4', title: '落地页信息' },
      ];
    }
    if (props.currentPage.mediaType === MediaType.KUAI_SHOU) {
      return [
        { key: '1', title: '账户信息' },
        { key: '2', title: '广告信息' },
        { key: '3', title: '创意信息' },
      ];
    }

    return [{ key: '1', title: '账户信息' }];
  });

  // 当前columns列表
  const columnsList = computed(() => {
    // 账户信息列配置
    const accountColumns: any = [
      {
        title: '操作对象',
        dataIndex: 'operationTargetName',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '操作内容',
        dataIndex: 'operationContent',
        slotName: 'operationContent',
        width: 350,
        ellipsis: true,
        minWidth: 150,
      },
      {
        title: '创建时间',
        dataIndex: 'createAt',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '执行结果',
        dataIndex: 'status',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
        render: ({ record }) => {
          // 执行结果 改为 该账户下全部内容替换完成 才显示结果，（全部成功=成功，全部失败=失败，有失败有成功=部分成功）
          const { success, fail, partSuccess } = TaskStatusMap;
          return [success, fail, partSuccess].includes(record.status)
            ? taskTableStatus.filter(
                (statusItem) => statusItem.value === record.status
              )[0].label
            : '-';
        },
      },
      {
        title: '失败原因',
        dataIndex: 'failReason',
        ellipsis: true,
        tooltip: true,
        render: ({ record }) => {
          return record.failReason ?? '-';
        },
        minWidth: 150,
      },
    ];
    // 如果任务状态为待执行，添加操作列
    if (baseInfo.value?.status === TaskStatusMap.wait) {
      accountColumns.push({
        title: '操作',
        dataIndex: 'operations',
        slotName: 'operations',
        fixed: 'right',
        width: 100,
      });
    }
    const ttColumns = [
      {
        title: '创建时间',
        dataIndex: 'createAt',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '更新时间',
        dataIndex: 'updateAt',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '执行结果',
        dataIndex: 'status',
        ellipsis: true,
        tooltip: true,
        minWidth: 150,
        render: ({ record }) => {
          return taskTableStatus.filter(
            (statusItem) => statusItem.value === record.status
          )[0]?.label;
        },
      },
      {
        title: '失败原因',
        dataIndex: 'failReason',
        ellipsis: true,
        tooltip: true,
        render: ({ record }) => {
          return record.failReason ?? '-';
        },
        minWidth: 150,
      },
    ];
    if (props.currentPage.mediaType === MediaType.TOU_TIAO) {
      return [
        accountColumns,
        [
          {
            title: '账户ID',
            dataIndex: 'advertiserId',
          },
          {
            title: '账户名称',
            dataIndex: 'advertiserName',
          },
          {
            title: '项目ID',
            dataIndex: 'campaignId',
          },
          {
            title: '项目名称',
            dataIndex: 'campaignName',
          },
          {
            title: '原监测链接',
            dataIndex: 'oldTrackUrl',
          },
          {
            title: '现监测链接',
            dataIndex: 'newTrackUrl',
          },
          ...ttColumns,
        ],
        [
          {
            title: '账户ID',
            dataIndex: 'advertiserId',
          },
          {
            title: '账户名称',
            dataIndex: 'advertiserName',
          },
          {
            title: '广告ID',
            dataIndex: 'unitId',
          },
          {
            title: '广告名称',
            dataIndex: 'unitName',
          },
          {
            title: '原快应用链接',
            dataIndex: 'oldTrackUrl',
          },
          {
            title: '现快应用链接',
            dataIndex: 'newTrackUrl',
          },
          ...ttColumns,
        ],
        [
          {
            title: '账户ID',
            dataIndex: 'advertiserId',
          },
          {
            title: '账户名称',
            dataIndex: 'advertiserName',
          },
          {
            title: '落地页ID',
            dataIndex: 'unitId',
          },
          {
            title: '落地页名称',
            dataIndex: 'unitName',
          },
          {
            title: '原快应用链接',
            dataIndex: 'oldTrackUrl',
          },
          {
            title: '现快应用链接',
            dataIndex: 'newTrackUrl',
          },
          ...ttColumns,
        ],
      ];
    }
    if (props.currentPage.mediaType === MediaType.KUAI_SHOU) {
      return [
        accountColumns,
        [
          {
            title: '账户ID',
            dataIndex: 'advertiserId',
          },
          {
            title: '账户名称',
            dataIndex: 'advertiserName',
          },
          {
            title: '广告组ID',
            dataIndex: 'campaignId',
          },
          {
            title: '广告组名称',
            dataIndex: 'campaignName',
          },
          {
            title: '原应用直达链接',
            dataIndex: 'oldTrackUrl',
          },
          {
            title: '现应用直达链接',
            dataIndex: 'newTrackUrl',
          },
          ...ttColumns,
        ],
        [
          {
            title: '账户ID',
            dataIndex: 'advertiserId',
          },
          {
            title: '账户名称',
            dataIndex: 'advertiserName',
          },
          {
            title: '创意ID',
            dataIndex: 'unitId',
          },
          {
            title: '创意名称',
            dataIndex: 'unitName',
          },
          {
            title: '原监测链接',
            dataIndex: 'oldTrackUrl',
          },
          {
            title: '现监测链接',
            dataIndex: 'newTrackUrl',
          },
          ...ttColumns,
        ],
      ];
    }

    return [accountColumns];
  });

  // 获取详情数据
  function fetchDetail() {
    operationTask.detail({ taskId: props.record.id }).then((res) => {
      baseInfo.value = res.data;

      res.data.taskDetailRespList.forEach((item) => {
        item.operationTargetValue = item.operationTargetValue
          ? JSON.parse(item.operationTargetValue)
          : [];
        item.originValue = item.originValue ? JSON.parse(item.originValue) : [];

        if (
          Array.isArray(item.operationTargetValue) &&
          Array.isArray(item.originValue)
        ) {
          item.showList = item.originValue.map((originItem, orIndex) => {
            return {
              ...originItem,
              oldPropertyName: originItem.propertyName,
              oldTargetName: originItem.targetName,
              newPropertyName: item.operationTargetValue[orIndex]?.propertyName,
              newTargetName: item.operationTargetValue[orIndex]?.targetName,
            };
          });
        } else {
          item.showList = [];
        }
      });

      tableDataList.value = [baseInfo.value.taskDetailRespList];
    });
  }

  // 获取操作列表
  async function getOperationList() {
    const { currentPage } = props;
    const res = await operationTask.getOperationTargetType({
      mediaPlatform: mediaPlatformMap[currentPage.mediaType],
      taskSource: 1,
    });
    operationTargetList.value = res.data;
  }

  // 取消操作
  function handleCancel(row) {
    Modal.open({
      title: '取消',
      content: `确定后不执行本次任务？`,
      onBeforeOk: (done) => {
        operationTask
          .detailStop({ id: row.id })
          .then(() => {
            fetchDetail();
            done(true);
            Message.success('操作成功');
          })
          .catch((err) => {
            done(false);
          });
      },
    });
  }

  // 初始化数据
  fetchDetail();
  getOperationList();
</script>

<style scoped lang="less">
  .operation-content {
    display: inline;
  }
</style>
