import { MediaType } from '@/views/promotion/hooks/authorization';

export type platformAccountType = {
  label: string;
  value: string;
  mediaType: MediaType;
};
export const platformAccounts: platformAccountType[] = [
  {
    label: '头条',
    value: 'tt',
    mediaType: MediaType.TOU_TIAO,
  },
  {
    label: '快手',
    value: 'ks',
    mediaType: MediaType.KUAI_SHOU,
  },
  {
    label: '广点通3.0',
    value: 'gdt',
    mediaType: MediaType.GDT,
  },
  {
    label: '百度',
    value: 'baidu',
    mediaType: MediaType.BAI_DU,
  },
  {
    label: '华为',
    value: 'huawei',
    mediaType: MediaType.HUA_WEI,
  },
  {
    label: 'vivo',
    value: 'vivo',
    mediaType: MediaType.VIVO,
  },
  {
    label: 'oppo',
    value: 'oppo',
    mediaType: MediaType.OPPO,
  },
  {
    label: '小米',
    value: 'xiaomi',
    mediaType: MediaType.XIAOMI,
  },
  {
    label: 'UC',
    value: 'uc',
    mediaType: MediaType.UC,
  },
  {
    label: '微博',
    value: 'weibo',
    mediaType: MediaType.WEI_BO,
  },
  {
    label: '趣头条',
    value: 'qtt',
    mediaType: MediaType.QU_TOU_TIAO,
  },
  {
    label: '支付宝',
    value: 'alipay',
    mediaType: MediaType.ALIPAY,
  },
  {
    label: 'WIFI万能钥匙',
    value: 'wifi',
    mediaType: MediaType.WIFI,
  },
  {
    label: '荣耀',
    value: 'honor',
    mediaType: MediaType.HONOR,
  },
  {
    label: '爱奇艺',
    value: 'iqiyi',
    mediaType: MediaType.IQIYI,
  },
];

export const taskTableStatus = [
  {
    label: '待执行',
    value: 1,
  },
  {
    label: '部分成功',
    value: 2,
  },
  {
    label: '成功',
    value: 3,
  },
  {
    label: '失败',
    value: 4,
  },
  {
    label: '执行中',
    value: 5,
  },
  {
    label: '终止执行',
    value: 6,
  },
];

export const TaskStatusMap = {
  wait: 1,
  partSuccess: 2,
  success: 3,
  fail: 4,
  running: 5,
  stop: 6,
};
